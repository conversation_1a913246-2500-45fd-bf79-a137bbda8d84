package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.Book;

import java.util.List;

/**
 * 图书服务接口
 */
public interface BookService {
    
    /**
     * 根据ID查询图书
     */
    Book findById(Long id);
    
    /**
     * 根据ISBN查询图书
     */
    Book findByIsbn(String isbn);
    
    /**
     * 查询所有上架的图书
     */
    List<Book> findAllAvailable();
    
    /**
     * 查询所有图书
     */
    List<Book> findAll();
    
    /**
     * 根据标题模糊查询
     */
    List<Book> findByTitleLike(String title);
    
    /**
     * 根据作者模糊查询
     */
    List<Book> findByAuthorLike(String author);
    
    /**
     * 根据分类查询
     */
    List<Book> findByCategory(String category);
    
    /**
     * 分页查询图书
     */
    List<Book> findByPage(int page, int size);
    
    /**
     * 统计上架图书总数
     */
    int countAvailable();
    
    /**
     * 保存图书
     */
    Book save(Book book);
    
    /**
     * 更新图书
     */
    Book update(Book book);
    
    /**
     * 删除图书
     */
    void deleteById(Long id);
    
    /**
     * 检查ISBN是否存在
     */
    boolean existsByIsbn(String isbn);
    
    /**
     * 检查ISBN是否存在（排除指定ID）
     */
    boolean existsByIsbnExcludeId(String isbn, Long excludeId);
    
    /**
     * 搜索图书（综合搜索）
     */
    List<Book> search(String keyword);
}

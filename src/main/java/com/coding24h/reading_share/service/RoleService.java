package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.Role;

import java.util.List;

/**
 * 角色服务接口
 */
public interface RoleService {
    
    /**
     * 根据ID查询角色
     */
    Role findById(Long id);
    
    /**
     * 根据角色代码查询角色
     */
    Role findByRoleCode(String roleCode);
    
    /**
     * 查询所有角色
     */
    List<Role> findAll();
    
    /**
     * 根据用户ID查询角色列表
     */
    List<Role> findByUserId(Long userId);
    
    /**
     * 保存角色
     */
    Role save(Role role);
    
    /**
     * 更新角色
     */
    Role update(Role role);
    
    /**
     * 删除角色
     */
    void deleteById(Long id);
    
    /**
     * 初始化默认角色
     */
    void initDefaultRoles();
}

package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 根据ID查询用户
     */
    User findById(Long id);
    
    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     */
    User findByEmail(String email);
    
    /**
     * 查询所有用户
     */
    List<User> findAll();
    
    /**
     * 分页查询用户
     */
    List<User> findByPage(int page, int size);
    
    /**
     * 统计用户总数
     */
    int count();
    
    /**
     * 保存用户
     */
    User save(User user);
    
    /**
     * 更新用户
     */
    User update(User user);
    
    /**
     * 删除用户
     */
    void deleteById(Long id);
    
    /**
     * 根据用户名模糊查询
     */
    List<User> findByUsernameLike(String username);
    
    /**
     * 用户注册
     */
    User register(User user);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 分配角色给用户
     */
    void assignRole(Long userId, Long roleId);
    
    /**
     * 移除用户角色
     */
    void removeRole(Long userId, Long roleId);
}

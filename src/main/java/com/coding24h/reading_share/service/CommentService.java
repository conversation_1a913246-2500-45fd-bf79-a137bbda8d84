package com.coding24h.reading_share.service;

import com.coding24h.reading_share.entity.Comment;

import java.util.List;

/**
 * 评论服务接口
 */
public interface CommentService {
    
    /**
     * 根据ID查询评论
     */
    Comment findById(Long id);
    
    /**
     * 根据笔记ID查询评论
     */
    List<Comment> findByNoteId(Long noteId);
    
    /**
     * 根据用户ID查询评论
     */
    List<Comment> findByUserId(Long userId);
    
    /**
     * 根据父评论ID查询回复
     */
    List<Comment> findByParentId(Long parentId);
    
    /**
     * 查询所有评论
     */
    List<Comment> findAll();
    
    /**
     * 分页查询评论
     */
    List<Comment> findByPage(int page, int size);
    
    /**
     * 查询评论总数
     */
    int count();
    
    /**
     * 保存评论
     */
    Comment save(Comment comment);
    
    /**
     * 更新评论
     */
    Comment update(Comment comment);
    
    /**
     * 删除评论
     */
    void deleteById(Long id);
    
    /**
     * 根据笔记ID删除所有评论
     */
    void deleteByNoteId(Long noteId);
    
    /**
     * 审核评论
     */
    void approve(Long commentId);
    
    /**
     * 拒绝评论
     */
    void reject(Long commentId);
    
    /**
     * 检查用户是否有权限删除评论
     */
    boolean hasDeletePermission(Long commentId, Long userId);
}

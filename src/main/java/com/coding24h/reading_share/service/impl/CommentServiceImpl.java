package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.entity.Comment;
import com.coding24h.reading_share.mapper.CommentMapper;
import com.coding24h.reading_share.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论服务实现类
 */
@Service
@Transactional
public class CommentServiceImpl implements CommentService {
    
    @Autowired
    private CommentMapper commentMapper;
    
    @Override
    public Comment findById(Long id) {
        return commentMapper.selectById(id);
    }
    
    @Override
    public List<Comment> findByNoteId(Long noteId) {
        return commentMapper.selectByNoteId(noteId);
    }
    
    @Override
    public List<Comment> findByUserId(Long userId) {
        return commentMapper.selectByUserId(userId);
    }
    
    @Override
    public List<Comment> findByParentId(Long parentId) {
        return commentMapper.selectByParentId(parentId);
    }
    
    @Override
    public List<Comment> findAll() {
        return commentMapper.selectAll();
    }
    
    @Override
    public List<Comment> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return commentMapper.selectByPage(offset, size);
    }
    
    @Override
    public int count() {
        return commentMapper.selectCount();
    }
    
    @Override
    public Comment save(Comment comment) {
        comment.setCreateTime(LocalDateTime.now());
        comment.setUpdateTime(LocalDateTime.now());
        if (comment.getStatus() == null) {
            comment.setStatus(1); // 默认已审核
        }
        commentMapper.insert(comment);
        return comment;
    }
    
    @Override
    public Comment update(Comment comment) {
        comment.setUpdateTime(LocalDateTime.now());
        commentMapper.update(comment);
        return comment;
    }
    
    @Override
    public void deleteById(Long id) {
        commentMapper.deleteById(id);
    }
    
    @Override
    public void deleteByNoteId(Long noteId) {
        commentMapper.deleteByNoteId(noteId);
    }
    
    @Override
    public void approve(Long commentId) {
        Comment comment = commentMapper.selectById(commentId);
        if (comment != null) {
            comment.setStatus(1); // 已审核
            update(comment);
        }
    }
    
    @Override
    public void reject(Long commentId) {
        Comment comment = commentMapper.selectById(commentId);
        if (comment != null) {
            comment.setStatus(2); // 已删除
            update(comment);
        }
    }
    
    @Override
    public boolean hasDeletePermission(Long commentId, Long userId) {
        Comment comment = commentMapper.selectById(commentId);
        if (comment == null) {
            return false;
        }
        
        // 只有评论作者可以删除自己的评论
        return comment.getUserId().equals(userId);
    }
}

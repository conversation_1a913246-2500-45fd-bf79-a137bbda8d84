package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.entity.Role;
import com.coding24h.reading_share.mapper.RoleMapper;
import com.coding24h.reading_share.mapper.UserRoleMapper;
import com.coding24h.reading_share.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色服务实现类
 */
@Service
@Transactional
public class RoleServiceImpl implements RoleService {
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Override
    public Role findById(Long id) {
        return roleMapper.selectById(id);
    }
    
    @Override
    public Role findByRoleCode(String roleCode) {
        return roleMapper.selectByRoleCode(roleCode);
    }
    
    @Override
    public List<Role> findAll() {
        return roleMapper.selectAll();
    }
    
    @Override
    public List<Role> findByUserId(Long userId) {
        return roleMapper.selectByUserId(userId);
    }
    
    @Override
    public Role save(Role role) {
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        if (role.getStatus() == null) {
            role.setStatus(1); // 默认启用
        }
        roleMapper.insert(role);
        return role;
    }
    
    @Override
    public Role update(Role role) {
        role.setUpdateTime(LocalDateTime.now());
        roleMapper.update(role);
        return role;
    }
    
    @Override
    public void deleteById(Long id) {
        // 先删除用户角色关联
        userRoleMapper.deleteByRoleId(id);
        // 再删除角色
        roleMapper.deleteById(id);
    }
    
    @Override
    public void initDefaultRoles() {
        // 检查是否已存在默认角色
        if (roleMapper.selectByRoleCode("ROLE_ADMIN") == null) {
            Role adminRole = new Role();
            adminRole.setRoleName("管理员");
            adminRole.setRoleCode("ROLE_ADMIN");
            adminRole.setDescription("系统管理员，拥有所有权限");
            adminRole.setStatus(1);
            save(adminRole);
        }
        
        if (roleMapper.selectByRoleCode("ROLE_USER") == null) {
            Role userRole = new Role();
            userRole.setRoleName("普通用户");
            userRole.setRoleCode("ROLE_USER");
            userRole.setDescription("普通用户，可以发布和管理自己的读书笔记");
            userRole.setStatus(1);
            save(userRole);
        }
    }
}

package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.entity.ReadingNote;
import com.coding24h.reading_share.mapper.ReadingNoteMapper;
import com.coding24h.reading_share.service.ReadingNoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 读书笔记服务实现类
 */
@Service
@Transactional
public class ReadingNoteServiceImpl implements ReadingNoteService {
    
    @Autowired
    private ReadingNoteMapper readingNoteMapper;
    
    @Override
    public ReadingNote findById(Long id) {
        return readingNoteMapper.selectById(id);
    }
    
    @Override
    public List<ReadingNote> findAllPublic() {
        return readingNoteMapper.selectAllPublic();
    }
    
    @Override
    public List<ReadingNote> findByUserId(Long userId) {
        return readingNoteMapper.selectByUserId(userId);
    }
    
    @Override
    public List<ReadingNote> findByBookId(Long bookId) {
        return readingNoteMapper.selectByBookId(bookId);
    }
    
    @Override
    public List<ReadingNote> findByCategoryId(Long categoryId) {
        return readingNoteMapper.selectByCategoryId(categoryId);
    }
    
    @Override
    public List<ReadingNote> findPublicByPage(int page, int size) {
        int offset = (page - 1) * size;
        return readingNoteMapper.selectPublicByPage(offset, size);
    }
    
    @Override
    public int countPublic() {
        return readingNoteMapper.selectPublicCount();
    }
    
    @Override
    public List<ReadingNote> findByTitleLike(String title) {
        return readingNoteMapper.selectByTitleLike(title);
    }
    
    @Override
    public List<ReadingNote> findByTags(String tags) {
        return readingNoteMapper.selectByTags(tags);
    }
    
    @Override
    public ReadingNote save(ReadingNote readingNote) {
        readingNote.setCreateTime(LocalDateTime.now());
        readingNote.setUpdateTime(LocalDateTime.now());
        if (readingNote.getIsPublic() == null) {
            readingNote.setIsPublic(0); // 默认私有
        }
        if (readingNote.getStatus() == null) {
            readingNote.setStatus(0); // 默认草稿
        }
        if (readingNote.getRating() == null) {
            readingNote.setRating(5); // 默认5星
        }
        readingNoteMapper.insert(readingNote);
        return readingNote;
    }
    
    @Override
    public ReadingNote update(ReadingNote readingNote) {
        readingNote.setUpdateTime(LocalDateTime.now());
        readingNoteMapper.update(readingNote);
        return readingNote;
    }
    
    @Override
    public void deleteById(Long id) {
        readingNoteMapper.deleteById(id);
    }
    
    @Override
    public List<ReadingNote> findByUserIdAndBookId(Long userId, Long bookId) {
        return readingNoteMapper.selectByUserIdAndBookId(userId, bookId);
    }
    
    @Override
    public List<ReadingNote> search(String keyword) {
        List<ReadingNote> results = new ArrayList<>();
        
        // 按标题搜索
        List<ReadingNote> titleResults = readingNoteMapper.selectByTitleLike(keyword);
        results.addAll(titleResults);
        
        // 按标签搜索
        List<ReadingNote> tagResults = readingNoteMapper.selectByTags(keyword);
        for (ReadingNote note : tagResults) {
            if (!results.contains(note)) {
                results.add(note);
            }
        }
        
        return results;
    }
    
    @Override
    public boolean hasPermission(Long noteId, Long userId) {
        ReadingNote note = readingNoteMapper.selectById(noteId);
        if (note == null) {
            return false;
        }
        
        // 如果是公开笔记，所有人都可以访问
        if (note.getIsPublic() == 1) {
            return true;
        }
        
        // 如果是私有笔记，只有作者可以访问
        return note.getUserId().equals(userId);
    }
    
    @Override
    public void publish(Long noteId) {
        ReadingNote note = readingNoteMapper.selectById(noteId);
        if (note != null) {
            note.setStatus(1); // 发布
            note.setIsPublic(1); // 公开
            update(note);
        }
    }
    
    @Override
    public void unpublish(Long noteId) {
        ReadingNote note = readingNoteMapper.selectById(noteId);
        if (note != null) {
            note.setStatus(0); // 草稿
            note.setIsPublic(0); // 私有
            update(note);
        }
    }
}

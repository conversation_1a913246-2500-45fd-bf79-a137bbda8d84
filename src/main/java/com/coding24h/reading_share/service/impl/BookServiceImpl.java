package com.coding24h.reading_share.service.impl;

import com.coding24h.reading_share.entity.Book;
import com.coding24h.reading_share.mapper.BookMapper;
import com.coding24h.reading_share.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 图书服务实现类
 */
@Service
@Transactional
public class BookServiceImpl implements BookService {
    
    @Autowired
    private BookMapper bookMapper;
    
    @Override
    public Book findById(Long id) {
        return bookMapper.selectById(id);
    }
    
    @Override
    public Book findByIsbn(String isbn) {
        return bookMapper.selectByIsbn(isbn);
    }
    
    @Override
    public List<Book> findAllAvailable() {
        return bookMapper.selectAllAvailable();
    }
    
    @Override
    public List<Book> findAll() {
        return bookMapper.selectAll();
    }
    
    @Override
    public List<Book> findByTitleLike(String title) {
        return bookMapper.selectByTitleLike(title);
    }
    
    @Override
    public List<Book> findByAuthorLike(String author) {
        return bookMapper.selectByAuthorLike(author);
    }
    
    @Override
    public List<Book> findByCategory(String category) {
        return bookMapper.selectByCategory(category);
    }
    
    @Override
    public List<Book> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return bookMapper.selectByPage(offset, size);
    }
    
    @Override
    public int countAvailable() {
        return bookMapper.countAvailable();
    }
    
    @Override
    public Book save(Book book) {
        book.setCreateTime(LocalDateTime.now());
        book.setUpdateTime(LocalDateTime.now());
        if (book.getStatus() == null) {
            book.setStatus(1); // 默认上架
        }
        bookMapper.insert(book);
        return book;
    }
    
    @Override
    public Book update(Book book) {
        book.setUpdateTime(LocalDateTime.now());
        bookMapper.update(book);
        return book;
    }
    
    @Override
    public void deleteById(Long id) {
        bookMapper.deleteById(id);
    }
    
    @Override
    public boolean existsByIsbn(String isbn) {
        return bookMapper.countByIsbn(isbn) > 0;
    }
    
    @Override
    public boolean existsByIsbnExcludeId(String isbn, Long excludeId) {
        return bookMapper.countByIsbnExcludeId(isbn, excludeId) > 0;
    }
    
    @Override
    public List<Book> search(String keyword) {
        List<Book> results = new ArrayList<>();
        
        // 按标题搜索
        List<Book> titleResults = bookMapper.selectByTitleLike(keyword);
        results.addAll(titleResults);
        
        // 按作者搜索
        List<Book> authorResults = bookMapper.selectByAuthorLike(keyword);
        for (Book book : authorResults) {
            if (!results.contains(book)) {
                results.add(book);
            }
        }
        
        return results;
    }
}

package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.NoteCategory;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 心得分类数据访问层 - 使用注解方式
 */
@Mapper
public interface NoteCategoryMapper {
    
    /**
     * 根据ID查询分类
     */
    @Select("SELECT id, name, description, color, sort_order, status, create_time, update_time " +
            "FROM note_categories WHERE id = #{id}")
    @Results({
        @Result(property = "sortOrder", column = "sort_order"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    NoteCategory selectById(@Param("id") Long id);
    
    /**
     * 根据名称查询分类
     */
    @Select("SELECT id, name, description, color, sort_order, status, create_time, update_time " +
            "FROM note_categories WHERE name = #{name}")
    @Results({
        @Result(property = "sortOrder", column = "sort_order"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    NoteCategory selectByName(@Param("name") String name);
    
    /**
     * 查询所有启用的分类，按排序顺序
     */
    @Select("SELECT id, name, description, color, sort_order, status, create_time, update_time " +
            "FROM note_categories WHERE status = 1 ORDER BY sort_order ASC, create_time ASC")
    @Results({
        @Result(property = "sortOrder", column = "sort_order"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<NoteCategory> selectAllEnabled();
    
    /**
     * 查询所有分类
     */
    @Select("SELECT id, name, description, color, sort_order, status, create_time, update_time " +
            "FROM note_categories ORDER BY sort_order ASC, create_time ASC")
    @Results({
        @Result(property = "sortOrder", column = "sort_order"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<NoteCategory> selectAll();
    
    /**
     * 插入分类
     */
    @Insert("INSERT INTO note_categories (name, description, color, sort_order, status, create_time, update_time) " +
            "VALUES (#{name}, #{description}, #{color}, #{sortOrder}, #{status}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(NoteCategory category);
    
    /**
     * 更新分类
     */
    @Update("UPDATE note_categories SET name = #{name}, description = #{description}, " +
            "color = #{color}, sort_order = #{sortOrder}, status = #{status}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int update(NoteCategory category);
    
    /**
     * 删除分类
     */
    @Delete("DELETE FROM note_categories WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 检查分类名称是否存在
     */
    @Select("SELECT COUNT(*) FROM note_categories WHERE name = #{name} AND id != #{excludeId}")
    int countByNameExcludeId(@Param("name") String name, @Param("excludeId") Long excludeId);
    
    /**
     * 获取最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM note_categories")
    int getMaxSortOrder();
}

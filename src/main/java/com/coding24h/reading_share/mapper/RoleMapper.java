package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Role;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 角色数据访问层 - 使用注解方式
 */
@Mapper
public interface RoleMapper {
    
    /**
     * 根据ID查询角色
     */
    @Select("SELECT id, role_name, role_code, description, status, create_time, update_time " +
            "FROM roles WHERE id = #{id}")
    @Results({
        @Result(property = "roleName", column = "role_name"),
        @Result(property = "roleCode", column = "role_code"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    Role selectById(@Param("id") Long id);
    
    /**
     * 根据角色代码查询角色
     */
    @Select("SELECT id, role_name, role_code, description, status, create_time, update_time " +
            "FROM roles WHERE role_code = #{roleCode}")
    @Results({
        @Result(property = "roleName", column = "role_name"),
        @Result(property = "roleCode", column = "role_code"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    Role selectByRoleCode(@Param("roleCode") String roleCode);
    
    /**
     * 查询所有角色
     */
    @Select("SELECT id, role_name, role_code, description, status, create_time, update_time " +
            "FROM roles ORDER BY create_time ASC")
    @Results({
        @Result(property = "roleName", column = "role_name"),
        @Result(property = "roleCode", column = "role_code"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Role> selectAll();
    
    /**
     * 根据用户ID查询角色列表
     */
    @Select("SELECT r.id, r.role_name, r.role_code, r.description, r.status, r.create_time, r.update_time " +
            "FROM roles r INNER JOIN user_roles ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId}")
    @Results({
        @Result(property = "roleName", column = "role_name"),
        @Result(property = "roleCode", column = "role_code"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Role> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 插入角色
     */
    @Insert("INSERT INTO roles (role_name, role_code, description, status, create_time, update_time) " +
            "VALUES (#{roleName}, #{roleCode}, #{description}, #{status}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Role role);
    
    /**
     * 更新角色
     */
    @Update("UPDATE roles SET role_name = #{roleName}, role_code = #{roleCode}, " +
            "description = #{description}, status = #{status}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int update(Role role);
    
    /**
     * 删除角色
     */
    @Delete("DELETE FROM roles WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 检查角色代码是否存在
     */
    @Select("SELECT COUNT(*) FROM roles WHERE role_code = #{roleCode}")
    int countByRoleCode(@Param("roleCode") String roleCode);
    
    /**
     * 检查角色代码是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM roles WHERE role_code = #{roleCode} AND id != #{excludeId}")
    int countByRoleCodeExcludeId(@Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);
}

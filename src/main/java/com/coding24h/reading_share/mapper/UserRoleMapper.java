package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.UserRole;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 用户角色关联数据访问层 - 使用注解方式
 */
@Mapper
public interface UserRoleMapper {
    
    /**
     * 根据ID查询用户角色关联
     */
    @Select("SELECT id, user_id, role_id, create_time FROM user_roles WHERE id = #{id}")
    @Results({
        @Result(property = "userId", column = "user_id"),
        @Result(property = "roleId", column = "role_id"),
        @Result(property = "createTime", column = "create_time")
    })
    UserRole selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询用户角色关联
     */
    @Select("SELECT id, user_id, role_id, create_time FROM user_roles WHERE user_id = #{userId}")
    @Results({
        @Result(property = "userId", column = "user_id"),
        @Result(property = "roleId", column = "role_id"),
        @Result(property = "createTime", column = "create_time")
    })
    List<UserRole> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID查询用户角色关联
     */
    @Select("SELECT id, user_id, role_id, create_time FROM user_roles WHERE role_id = #{roleId}")
    @Results({
        @Result(property = "userId", column = "user_id"),
        @Result(property = "roleId", column = "role_id"),
        @Result(property = "createTime", column = "create_time")
    })
    List<UserRole> selectByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 查询所有用户角色关联
     */
    @Select("SELECT id, user_id, role_id, create_time FROM user_roles ORDER BY create_time DESC")
    @Results({
        @Result(property = "userId", column = "user_id"),
        @Result(property = "roleId", column = "role_id"),
        @Result(property = "createTime", column = "create_time")
    })
    List<UserRole> selectAll();
    
    /**
     * 插入用户角色关联
     */
    @Insert("INSERT INTO user_roles (user_id, role_id, create_time) VALUES (#{userId}, #{roleId}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserRole userRole);
    
    /**
     * 删除用户角色关联
     */
    @Delete("DELETE FROM user_roles WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据用户ID删除用户角色关联
     */
    @Delete("DELETE FROM user_roles WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID删除用户角色关联
     */
    @Delete("DELETE FROM user_roles WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户ID和角色ID删除用户角色关联
     */
    @Delete("DELETE FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}")
    int deleteByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 检查用户角色关联是否存在
     */
    @Select("SELECT COUNT(*) FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}")
    int countByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
}

package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Comment;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 评论数据访问层 - 使用注解方式
 */
@Mapper
public interface CommentMapper {
    
    /**
     * 根据ID查询评论（包含关联信息）
     */
    @Select("SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time, " +
            "u.username, u.nickname, u.avatar " +
            "FROM comments c LEFT JOIN users u ON c.user_id = u.id " +
            "WHERE c.id = #{id}")
    @Results({
        @Result(property = "noteId", column = "note_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "parentId", column = "parent_id"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "user", javaType = com.coding24h.reading_share.entity.User.class, 
                column = "user_id", one = @One(select = "selectUserById"))
    })
    Comment selectById(@Param("id") Long id);
    
    /**
     * 根据笔记ID查询评论列表
     */
    @Select("SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time, " +
            "u.username, u.nickname, u.avatar " +
            "FROM comments c LEFT JOIN users u ON c.user_id = u.id " +
            "WHERE c.note_id = #{noteId} AND c.status = 1 ORDER BY c.create_time ASC")
    @Results({
        @Result(property = "noteId", column = "note_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "parentId", column = "parent_id"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "user", javaType = com.coding24h.reading_share.entity.User.class, 
                column = "user_id", one = @One(select = "selectUserById"))
    })
    List<Comment> selectByNoteId(@Param("noteId") Long noteId);
    
    /**
     * 根据用户ID查询评论列表
     */
    @Select("SELECT id, note_id, user_id, content, parent_id, status, create_time, update_time " +
            "FROM comments WHERE user_id = #{userId} ORDER BY create_time DESC")
    @Results({
        @Result(property = "noteId", column = "note_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "parentId", column = "parent_id"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Comment> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据父评论ID查询回复列表
     */
    @Select("SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time, " +
            "u.username, u.nickname, u.avatar " +
            "FROM comments c LEFT JOIN users u ON c.user_id = u.id " +
            "WHERE c.parent_id = #{parentId} AND c.status = 1 ORDER BY c.create_time ASC")
    @Results({
        @Result(property = "noteId", column = "note_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "parentId", column = "parent_id"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "user", javaType = com.coding24h.reading_share.entity.User.class, 
                column = "user_id", one = @One(select = "selectUserById"))
    })
    List<Comment> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询待审核的评论
     */
    @Select("SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time, " +
            "u.username, u.nickname, u.avatar " +
            "FROM comments c LEFT JOIN users u ON c.user_id = u.id " +
            "WHERE c.status = 0 ORDER BY c.create_time DESC")
    @Results({
        @Result(property = "noteId", column = "note_id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "parentId", column = "parent_id"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "user", javaType = com.coding24h.reading_share.entity.User.class, 
                column = "user_id", one = @One(select = "selectUserById"))
    })
    List<Comment> selectPendingComments();
    
    /**
     * 统计笔记的评论数量
     */
    @Select("SELECT COUNT(*) FROM comments WHERE note_id = #{noteId} AND status = 1")
    int countByNoteId(@Param("noteId") Long noteId);
    
    /**
     * 插入评论
     */
    @Insert("INSERT INTO comments (note_id, user_id, content, parent_id, status, create_time, update_time) " +
            "VALUES (#{noteId}, #{userId}, #{content}, #{parentId}, #{status}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Comment comment);
    
    /**
     * 更新评论
     */
    @Update("UPDATE comments SET content = #{content}, status = #{status}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int update(Comment comment);
    
    /**
     * 删除评论
     */
    @Delete("DELETE FROM comments WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据笔记ID删除所有评论
     */
    @Delete("DELETE FROM comments WHERE note_id = #{noteId}")
    int deleteByNoteId(@Param("noteId") Long noteId);
    
    /**
     * 审核评论（通过）
     */
    @Update("UPDATE comments SET status = 1, update_time = NOW() WHERE id = #{id}")
    int approve(@Param("id") Long id);
    
    /**
     * 拒绝评论
     */
    @Update("UPDATE comments SET status = 2, update_time = NOW() WHERE id = #{id}")
    int reject(@Param("id") Long id);
    
    // 辅助查询方法
    @Select("SELECT id, username, nickname, avatar FROM users WHERE id = #{id}")
    com.coding24h.reading_share.entity.User selectUserById(@Param("id") Long id);
}

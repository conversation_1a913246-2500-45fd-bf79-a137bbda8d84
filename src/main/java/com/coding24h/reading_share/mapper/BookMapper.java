package com.coding24h.reading_share.mapper;

import com.coding24h.reading_share.entity.Book;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 图书数据访问层 - 使用注解方式
 */
@Mapper
public interface BookMapper {
    
    /**
     * 根据ID查询图书
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books WHERE id = #{id}")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    Book selectById(@Param("id") Long id);
    
    /**
     * 根据ISBN查询图书
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books WHERE isbn = #{isbn}")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    Book selectByIsbn(@Param("isbn") String isbn);
    
    /**
     * 查询所有上架的图书
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books WHERE status = 1 ORDER BY create_time DESC")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Book> selectAllAvailable();
    
    /**
     * 查询所有图书
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books ORDER BY create_time DESC")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Book> selectAll();
    
    /**
     * 根据标题模糊查询
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books WHERE title LIKE CONCAT('%', #{title}, '%') AND status = 1")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Book> selectByTitleLike(@Param("title") String title);
    
    /**
     * 根据作者模糊查询
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books WHERE author LIKE CONCAT('%', #{author}, '%') AND status = 1")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Book> selectByAuthorLike(@Param("author") String author);
    
    /**
     * 根据分类查询
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books WHERE category = #{category} AND status = 1")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Book> selectByCategory(@Param("category") String category);
    
    /**
     * 分页查询图书
     */
    @Select("SELECT id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time " +
            "FROM books WHERE status = 1 ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    @Results({
        @Result(property = "publishDate", column = "publish_date"),
        @Result(property = "coverImage", column = "cover_image"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time")
    })
    List<Book> selectByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计上架图书总数
     */
    @Select("SELECT COUNT(*) FROM books WHERE status = 1")
    int countAvailable();
    
    /**
     * 插入图书
     */
    @Insert("INSERT INTO books (title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time) " +
            "VALUES (#{title}, #{author}, #{isbn}, #{publisher}, #{publishDate}, #{category}, #{description}, #{coverImage}, #{status}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Book book);
    
    /**
     * 更新图书
     */
    @Update("UPDATE books SET title = #{title}, author = #{author}, isbn = #{isbn}, publisher = #{publisher}, " +
            "publish_date = #{publishDate}, category = #{category}, description = #{description}, " +
            "cover_image = #{coverImage}, status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    int update(Book book);
    
    /**
     * 删除图书
     */
    @Delete("DELETE FROM books WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 检查ISBN是否存在
     */
    @Select("SELECT COUNT(*) FROM books WHERE isbn = #{isbn}")
    int countByIsbn(@Param("isbn") String isbn);
    
    /**
     * 检查ISBN是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM books WHERE isbn = #{isbn} AND id != #{excludeId}")
    int countByIsbnExcludeId(@Param("isbn") String isbn, @Param("excludeId") Long excludeId);
}

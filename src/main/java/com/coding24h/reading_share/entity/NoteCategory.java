package com.coding24h.reading_share.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 心得分类实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NoteCategory {
    private Long id;
    private String name;
    private String description;
    private String color; // 分类颜色标识
    private Integer sortOrder; // 排序顺序
    private Integer status; // 0-禁用 1-启用
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

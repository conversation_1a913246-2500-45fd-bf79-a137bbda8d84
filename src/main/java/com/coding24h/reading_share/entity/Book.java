package com.coding24h.reading_share.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图书实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Book {
    private Long id;
    private String title;
    private String author;
    private String isbn;
    private String publisher;
    private String publishDate;
    private String category;
    private String description;
    private String coverImage;
    private Integer status; // 0-下架 1-上架
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

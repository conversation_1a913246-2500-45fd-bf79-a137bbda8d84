package com.coding24h.reading_share.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 评论实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Comment {
    private Long id;
    private Long noteId;
    private Long userId;
    private String content;
    private Long parentId; // 父评论ID，用于回复功能
    private Integer status; // 0-待审核 1-已审核 2-已删除
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 关联对象
    private User user;
    private ReadingNote readingNote;
}

package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.NoteCategory;
import com.coding24h.reading_share.service.NoteCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 心得分类控制器
 */
@Controller
@RequestMapping("/categories")
public class NoteCategoryController {
    
    @Autowired
    private NoteCategoryService categoryService;
    
    /**
     * 分类列表页面
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public String list(Model model) {
        List<NoteCategory> categories = categoryService.findAll();
        model.addAttribute("categories", categories);
        return "categories/list";
    }
    
    /**
     * 新增分类页面
     */
    @GetMapping("/add")
    @PreAuthorize("hasRole('ADMIN')")
    public String addForm(Model model) {
        model.addAttribute("category", new NoteCategory());
        return "categories/form";
    }
    
    /**
     * 编辑分类页面
     */
    @GetMapping("/edit/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String editForm(@PathVariable Long id, Model model) {
        NoteCategory category = categoryService.findById(id);
        if (category == null) {
            return "error/404";
        }
        model.addAttribute("category", category);
        return "categories/form";
    }
    
    /**
     * 保存分类
     */
    @PostMapping("/save")
    @PreAuthorize("hasRole('ADMIN')")
    public String save(@Valid @ModelAttribute NoteCategory category, 
                      BindingResult result, 
                      RedirectAttributes redirectAttributes) {
        
        // 验证分类名称是否重复
        if (category.getId() == null) {
            // 新增时检查名称是否存在
            if (categoryService.existsByName(category.getName())) {
                result.rejectValue("name", "error.category", "分类名称已存在");
            }
        } else {
            // 编辑时检查名称是否重复（排除自己）
            if (categoryService.existsByNameExcludeId(category.getName(), category.getId())) {
                result.rejectValue("name", "error.category", "分类名称已存在");
            }
        }
        
        if (result.hasErrors()) {
            return "categories/form";
        }
        
        try {
            if (category.getId() == null) {
                categoryService.save(category);
                redirectAttributes.addFlashAttribute("successMessage", "分类创建成功");
            } else {
                categoryService.update(category);
                redirectAttributes.addFlashAttribute("successMessage", "分类更新成功");
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "操作失败：" + e.getMessage());
        }
        
        return "redirect:/categories";
    }
    
    /**
     * 删除分类
     */
    @PostMapping("/delete/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String delete(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            categoryService.deleteById(id);
            redirectAttributes.addFlashAttribute("successMessage", "分类删除成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "删除失败：" + e.getMessage());
        }
        return "redirect:/categories";
    }
    
    /**
     * API: 获取所有启用的分类（用于前端选择）
     */
    @GetMapping("/api/enabled")
    @ResponseBody
    public List<NoteCategory> getEnabledCategories() {
        return categoryService.findAllEnabled();
    }
}

package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Book;
import com.coding24h.reading_share.entity.ReadingNote;
import com.coding24h.reading_share.service.BookService;
import com.coding24h.reading_share.service.ReadingNoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;

/**
 * 图书控制器
 */
@Controller
@RequestMapping("/books")
public class BookController {
    
    @Autowired
    private BookService bookService;
    
    @Autowired
    private ReadingNoteService readingNoteService;
    
    @GetMapping
    public String list(@RequestParam(defaultValue = "1") int page,
                      @RequestParam(defaultValue = "12") int size,
                      Model model) {
        List<Book> books = bookService.findByPage(page, size);
        int totalCount = bookService.count();
        int totalPages = (int) Math.ceil((double) totalCount / size);
        
        model.addAttribute("books", books);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("totalCount", totalCount);
        
        return "books/list";
    }
    
    @GetMapping("/{id}")
    public String detail(@PathVariable Long id, Model model) {
        Book book = bookService.findById(id);
        if (book == null) {
            return "error/404";
        }
        
        // 获取该图书的读书笔记
        List<ReadingNote> notes = readingNoteService.findByBookId(id);
        
        model.addAttribute("book", book);
        model.addAttribute("notes", notes);
        
        return "books/detail";
    }
    
    @GetMapping("/category/{category}")
    public String category(@PathVariable String category, Model model) {
        List<Book> books = bookService.findByCategory(category);
        model.addAttribute("books", books);
        model.addAttribute("category", category);
        
        return "books/category";
    }
    
    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("/add")
    public String add(Model model) {
        model.addAttribute("book", new Book());
        return "books/form";
    }
    
    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/add")
    public String add(@ModelAttribute("book") Book book, 
                     BindingResult result, 
                     RedirectAttributes redirectAttributes) {
        
        // 基本验证
        if (book.getTitle() == null || book.getTitle().trim().isEmpty()) {
            result.rejectValue("title", "error.book", "书名不能为空");
        }
        
        if (book.getAuthor() == null || book.getAuthor().trim().isEmpty()) {
            result.rejectValue("author", "error.book", "作者不能为空");
        }
        
        if (book.getIsbn() != null && !book.getIsbn().trim().isEmpty() && 
            bookService.existsByIsbn(book.getIsbn())) {
            result.rejectValue("isbn", "error.book", "ISBN已存在");
        }
        
        if (result.hasErrors()) {
            return "books/form";
        }
        
        try {
            bookService.save(book);
            redirectAttributes.addFlashAttribute("success", "图书添加成功");
            return "redirect:/books";
        } catch (Exception e) {
            result.rejectValue("title", "error.book", "添加失败：" + e.getMessage());
            return "books/form";
        }
    }
    
    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("/{id}/edit")
    public String edit(@PathVariable Long id, Model model) {
        Book book = bookService.findById(id);
        if (book == null) {
            return "error/404";
        }
        
        model.addAttribute("book", book);
        return "books/form";
    }
    
    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/{id}/edit")
    public String edit(@PathVariable Long id,
                      @ModelAttribute("book") Book book, 
                      BindingResult result, 
                      RedirectAttributes redirectAttributes) {
        
        book.setId(id);
        
        // 基本验证
        if (book.getTitle() == null || book.getTitle().trim().isEmpty()) {
            result.rejectValue("title", "error.book", "书名不能为空");
        }
        
        if (book.getAuthor() == null || book.getAuthor().trim().isEmpty()) {
            result.rejectValue("author", "error.book", "作者不能为空");
        }
        
        // 检查ISBN是否被其他图书使用
        if (book.getIsbn() != null && !book.getIsbn().trim().isEmpty()) {
            Book existingBook = bookService.findByIsbn(book.getIsbn());
            if (existingBook != null && !existingBook.getId().equals(id)) {
                result.rejectValue("isbn", "error.book", "ISBN已被其他图书使用");
            }
        }
        
        if (result.hasErrors()) {
            return "books/form";
        }
        
        try {
            bookService.update(book);
            redirectAttributes.addFlashAttribute("success", "图书更新成功");
            return "redirect:/books/" + id;
        } catch (Exception e) {
            result.rejectValue("title", "error.book", "更新失败：" + e.getMessage());
            return "books/form";
        }
    }
    
    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/{id}/delete")
    public String delete(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            bookService.deleteById(id);
            redirectAttributes.addFlashAttribute("success", "图书删除成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "删除失败：" + e.getMessage());
        }
        
        return "redirect:/books";
    }
}

package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.NoteCategory;
import com.coding24h.reading_share.entity.ReadingNote;
import com.coding24h.reading_share.service.NoteCategoryService;
import com.coding24h.reading_share.service.ReadingNoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 主页控制器
 */
@Controller
public class HomeController {
    
    @Autowired
    private ReadingNoteService readingNoteService;
    
    @Autowired
    private NoteCategoryService categoryService;
    
    /**
     * 首页
     */
    @GetMapping({"/", "/home"})
    public String home(Model model) {
        // 获取最新的公开笔记
        List<ReadingNote> latestNotes = readingNoteService.findPublicByPage(1, 6);
        List<NoteCategory> categories = categoryService.findAllEnabled();
        
        model.addAttribute("latestNotes", latestNotes);
        model.addAttribute("categories", categories);
        
        return "index";
    }
    
    /**
     * 仪表板页面（登录后的主页）
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        // 获取最新的公开笔记
        List<ReadingNote> latestNotes = readingNoteService.findPublicByPage(1, 10);
        List<NoteCategory> categories = categoryService.findAllEnabled();
        
        model.addAttribute("latestNotes", latestNotes);
        model.addAttribute("categories", categories);
        
        return "dashboard";
    }
}

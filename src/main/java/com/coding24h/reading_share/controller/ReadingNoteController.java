package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.Book;
import com.coding24h.reading_share.entity.NoteCategory;
import com.coding24h.reading_share.entity.ReadingNote;
import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.BookService;
import com.coding24h.reading_share.service.NoteCategoryService;
import com.coding24h.reading_share.service.ReadingNoteService;
import com.coding24h.reading_share.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.validation.Valid;
import java.util.List;

/**
 * 读书笔记控制器
 */
@Controller
@RequestMapping("/notes")
public class ReadingNoteController {
    
    @Autowired
    private ReadingNoteService readingNoteService;
    
    @Autowired
    private BookService bookService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private NoteCategoryService categoryService;
    
    /**
     * 公开笔记列表页面
     */
    @GetMapping
    public String list(Model model, 
                      @RequestParam(defaultValue = "1") int page,
                      @RequestParam(defaultValue = "10") int size,
                      @RequestParam(required = false) Long categoryId) {
        
        List<ReadingNote> notes;
        if (categoryId != null) {
            notes = readingNoteService.findByCategoryId(categoryId);
        } else {
            notes = readingNoteService.findPublicByPage(page, size);
        }
        
        List<NoteCategory> categories = categoryService.findAllEnabled();
        
        model.addAttribute("notes", notes);
        model.addAttribute("categories", categories);
        model.addAttribute("currentCategoryId", categoryId);
        model.addAttribute("currentPage", page);
        
        return "notes/list";
    }
    
    /**
     * 我的笔记列表页面
     */
    @GetMapping("/my")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public String myNotes(Model model) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        List<ReadingNote> notes = readingNoteService.findByUserId(currentUser.getId());
        model.addAttribute("notes", notes);
        
        return "notes/my-list";
    }
    
    /**
     * 笔记详情页面
     */
    @GetMapping("/{id}")
    public String detail(@PathVariable Long id, Model model) {
        ReadingNote note = readingNoteService.findById(id);
        if (note == null) {
            return "error/404";
        }
        
        // 检查权限
        User currentUser = getCurrentUser();
        if (!readingNoteService.hasPermission(id, currentUser != null ? currentUser.getId() : null)) {
            return "error/403";
        }
        
        model.addAttribute("note", note);
        
        return "notes/detail";
    }
    
    /**
     * 新增笔记页面
     */
    @GetMapping("/add")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public String addForm(Model model) {
        model.addAttribute("note", new ReadingNote());
        model.addAttribute("books", bookService.findAllAvailable());
        model.addAttribute("categories", categoryService.findAllEnabled());
        return "notes/form";
    }
    
    /**
     * 编辑笔记页面
     */
    @GetMapping("/edit/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public String editForm(@PathVariable Long id, Model model) {
        ReadingNote note = readingNoteService.findById(id);
        if (note == null) {
            return "error/404";
        }
        
        User currentUser = getCurrentUser();
        if (currentUser == null || !note.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        model.addAttribute("note", note);
        model.addAttribute("books", bookService.findAllAvailable());
        model.addAttribute("categories", categoryService.findAllEnabled());
        return "notes/form";
    }
    
    /**
     * 保存笔记
     */
    @PostMapping("/save")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public String save(@Valid @ModelAttribute ReadingNote note, 
                      BindingResult result, 
                      RedirectAttributes redirectAttributes,
                      Model model) {
        
        if (result.hasErrors()) {
            model.addAttribute("books", bookService.findAllAvailable());
            model.addAttribute("categories", categoryService.findAllEnabled());
            return "notes/form";
        }
        
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return "redirect:/login";
        }
        
        try {
            if (note.getId() == null) {
                // 新增
                note.setUserId(currentUser.getId());
                readingNoteService.save(note);
                redirectAttributes.addFlashAttribute("successMessage", "笔记创建成功");
            } else {
                // 编辑 - 检查权限
                ReadingNote existingNote = readingNoteService.findById(note.getId());
                if (existingNote == null || !existingNote.getUserId().equals(currentUser.getId())) {
                    return "error/403";
                }
                note.setUserId(currentUser.getId());
                readingNoteService.update(note);
                redirectAttributes.addFlashAttribute("successMessage", "笔记更新成功");
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "操作失败：" + e.getMessage());
        }
        
        return "redirect:/notes/my";
    }
    
    /**
     * 删除笔记
     */
    @PostMapping("/delete/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public String delete(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        ReadingNote note = readingNoteService.findById(id);
        if (note == null) {
            redirectAttributes.addFlashAttribute("errorMessage", "笔记不存在");
            return "redirect:/notes/my";
        }
        
        User currentUser = getCurrentUser();
        if (currentUser == null || !note.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        try {
            readingNoteService.deleteById(id);
            redirectAttributes.addFlashAttribute("successMessage", "笔记删除成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "删除失败：" + e.getMessage());
        }
        
        return "redirect:/notes/my";
    }
    
    /**
     * 发布笔记
     */
    @PostMapping("/publish/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public String publish(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        ReadingNote note = readingNoteService.findById(id);
        if (note == null) {
            redirectAttributes.addFlashAttribute("errorMessage", "笔记不存在");
            return "redirect:/notes/my";
        }
        
        User currentUser = getCurrentUser();
        if (currentUser == null || !note.getUserId().equals(currentUser.getId())) {
            return "error/403";
        }
        
        try {
            readingNoteService.publish(id);
            redirectAttributes.addFlashAttribute("successMessage", "笔记发布成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "发布失败：" + e.getMessage());
        }
        
        return "redirect:/notes/my";
    }
    
    /**
     * 搜索笔记
     */
    @GetMapping("/search")
    public String search(@RequestParam String keyword, Model model) {
        List<ReadingNote> notes = readingNoteService.search(keyword);
        List<NoteCategory> categories = categoryService.findAllEnabled();
        
        model.addAttribute("notes", notes);
        model.addAttribute("categories", categories);
        model.addAttribute("keyword", keyword);
        
        return "notes/search-results";
    }
    
    /**
     * 获取当前登录用户
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && 
            !authentication.getName().equals("anonymousUser")) {
            return userService.findByUsername(authentication.getName());
        }
        return null;
    }
}

package com.coding24h.reading_share.controller;

import com.coding24h.reading_share.entity.User;
import com.coding24h.reading_share.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

/**
 * 认证控制器
 */
@Controller
public class AuthController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping("/login")
    public String login() {
        return "auth/login";
    }
    
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("user", new User());
        return "auth/register";
    }
    
    @PostMapping("/register")
    public String register(@ModelAttribute("user") User user, 
                          BindingResult result, 
                          RedirectAttributes redirectAttributes) {
        
        // 基本验证
        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            result.rejectValue("username", "error.user", "用户名不能为空");
        }
        
        if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
            result.rejectValue("password", "error.user", "密码不能为空");
        }
        
        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            result.rejectValue("email", "error.user", "邮箱不能为空");
        }
        
        // 检查用户名是否已存在
        if (userService.existsByUsername(user.getUsername())) {
            result.rejectValue("username", "error.user", "用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userService.existsByEmail(user.getEmail())) {
            result.rejectValue("email", "error.user", "邮箱已存在");
        }
        
        if (result.hasErrors()) {
            return "auth/register";
        }
        
        try {
            userService.register(user);
            redirectAttributes.addFlashAttribute("success", "注册成功，请登录");
            return "redirect:/login";
        } catch (Exception e) {
            result.rejectValue("username", "error.user", "注册失败：" + e.getMessage());
            return "auth/register";
        }
    }
    
    @GetMapping("/dashboard")
    public String dashboard() {
        return "dashboard";
    }
}

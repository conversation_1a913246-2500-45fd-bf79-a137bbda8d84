<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 阅读分享平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem 0;
        }
        .register-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="register-card">
                        <div class="register-header">
                            <h2 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                用户注册
                            </h2>
                            <p class="mb-0 mt-2">加入阅读分享社区</p>
                        </div>
                        
                        <div class="register-body">
                            <!-- 注册表单 -->
                            <form th:action="@{/register}" method="post" th:object="${user}">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>用户名 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="username" th:field="*{username}" 
                                           placeholder="请输入用户名" required>
                                    <div th:if="${#fields.hasErrors('username')}" class="text-danger small mt-1">
                                        <span th:errors="*{username}"></span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>邮箱 <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" class="form-control" id="email" th:field="*{email}" 
                                           placeholder="请输入邮箱地址" required>
                                    <div th:if="${#fields.hasErrors('email')}" class="text-danger small mt-1">
                                        <span th:errors="*{email}"></span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nickname" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>昵称
                                    </label>
                                    <input type="text" class="form-control" id="nickname" th:field="*{nickname}" 
                                           placeholder="请输入昵称（可选）">
                                    <div class="form-text">如不填写，将使用用户名作为昵称</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>密码 <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" class="form-control" id="password" th:field="*{password}" 
                                           placeholder="请输入密码" required>
                                    <div th:if="${#fields.hasErrors('password')}" class="text-danger small mt-1">
                                        <span th:errors="*{password}"></span>
                                    </div>
                                    <div class="form-text">密码长度至少6位</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">
                                        <i class="fas fa-lock me-1"></i>确认密码 <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" class="form-control" id="confirmPassword" 
                                           placeholder="请再次输入密码" required>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="agreement" required>
                                    <label class="form-check-label" for="agreement">
                                        我已阅读并同意 <a href="#" class="text-decoration-none">用户协议</a> 和 <a href="#" class="text-decoration-none">隐私政策</a>
                                    </label>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-user-plus me-2"></i>注册
                                    </button>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-0">已有账号？ 
                                    <a th:href="@{/login}" class="text-decoration-none">立即登录</a>
                                </p>
                                <p class="mt-2">
                                    <a th:href="@{/}" class="text-decoration-none">
                                        <i class="fas fa-arrow-left me-1"></i>返回首页
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 密码确认验证
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('密码不匹配');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>

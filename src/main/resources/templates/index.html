<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>读书分享平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">读书分享平台</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/notes">浏览笔记</a>
                <a class="nav-link" href="/login">登录</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <h2>最新读书笔记</h2>
                <div class="row" th:if="${latestNotes != null and !latestNotes.isEmpty()}">
                    <div class="col-md-6 mb-3" th:each="note : ${latestNotes}">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title" th:text="${note.title}">笔记标题</h5>
                                <p class="card-text" th:text="${#strings.abbreviate(note.content, 100)}">笔记内容摘要...</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted" th:text="${note.user?.nickname}">作者</small>
                                    <a href="#" class="btn btn-sm btn-outline-primary" th:href="@{/notes/{id}(id=${note.id})}">阅读更多</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:if="${latestNotes == null or latestNotes.isEmpty()}">
                    <p class="text-muted">暂无公开的读书笔记</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <h4>分类浏览</h4>
                <div class="list-group" th:if="${categories != null and !categories.isEmpty()}">
                    <a href="#" class="list-group-item list-group-item-action" 
                       th:each="category : ${categories}"
                       th:href="@{/notes(categoryId=${category.id})}"
                       th:text="${category.name}">分类名称</a>
                </div>
                <div th:if="${categories == null or categories.isEmpty()}">
                    <p class="text-muted">暂无分类</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

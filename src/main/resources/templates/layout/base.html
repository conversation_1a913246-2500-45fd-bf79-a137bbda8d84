<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title != null ? title + ' - 阅读分享平台' : '阅读分享平台'}">阅读分享平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link th:href="@{/css/style.css}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-book-reader me-2"></i>阅读分享
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/books}">图书</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/notes/public}">公开笔记</a>
                    </li>
                    <li class="nav-item" sec:authorize="isAuthenticated()">
                        <a class="nav-link" th:href="@{/notes/my}">我的笔记</a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <form class="d-flex me-3" th:action="@{/search}" method="get">
                    <input class="form-control me-2" type="search" name="keyword" placeholder="搜索图书或笔记..." style="width: 200px;">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <!-- 用户菜单 -->
                <ul class="navbar-nav">
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/login}">登录</a>
                    </li>
                    <li class="nav-item" sec:authorize="!isAuthenticated()">
                        <a class="nav-link" th:href="@{/register}">注册</a>
                    </li>
                    <li class="nav-item dropdown" sec:authorize="isAuthenticated()">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" th:href="@{/dashboard}">控制台</a></li>
                            <li><a class="dropdown-item" th:href="@{/notes/add}">写笔记</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item">退出登录</button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <main class="container mt-4">
        <!-- 消息提示 -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><span th:text="${success}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        
        <!-- 页面内容 -->
        <div th:replace="${content}">页面内容</div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>阅读分享平台</h5>
                    <p class="text-muted">分享阅读心得，传递知识力量</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">
                        &copy; 2024 阅读分享平台. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/app.js}"></script>
</body>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.BookMapper">

    <resultMap id="BaseResultMap" type="com.coding24h.reading_share.entity.Book">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="author" property="author" jdbcType="VARCHAR"/>
        <result column="isbn" property="isbn" jdbcType="VARCHAR"/>
        <result column="publisher" property="publisher" jdbcType="VARCHAR"/>
        <result column="publish_date" property="publishDate" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="TEXT"/>
        <result column="cover_image" property="coverImage" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM books
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM books
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM books
        WHERE status = 1
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectCount" resultType="int">
        SELECT COUNT(*)
        FROM books
        WHERE status = 1
    </select>

    <select id="selectByTitleLike" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM books
        WHERE title LIKE CONCAT('%', #{title}, '%') AND status = 1
        ORDER BY create_time DESC
    </select>

    <select id="selectByAuthorLike" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM books
        WHERE author LIKE CONCAT('%', #{author}, '%') AND status = 1
        ORDER BY create_time DESC
    </select>

    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM books
        WHERE category = #{category} AND status = 1
        ORDER BY create_time DESC
    </select>

    <select id="selectByIsbn" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM books
        WHERE isbn = #{isbn}
    </select>

    <insert id="insert" parameterType="com.coding24h.reading_share.entity.Book" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO books (title, author, isbn, publisher, publish_date, category, description, cover_image, status, create_time, update_time)
        VALUES (#{title}, #{author}, #{isbn}, #{publisher}, #{publishDate}, #{category}, #{description}, #{coverImage}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <update id="update" parameterType="com.coding24h.reading_share.entity.Book">
        UPDATE books
        SET title = #{title},
            author = #{author},
            isbn = #{isbn},
            publisher = #{publisher},
            publish_date = #{publishDate},
            category = #{category},
            description = #{description},
            cover_image = #{coverImage},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM books WHERE id = #{id}
    </delete>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="com.coding24h.reading_share.entity.Role">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, role_name, role_code, description, status, create_time, update_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM roles
        WHERE id = #{id}
    </select>

    <select id="selectByRoleCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM roles
        WHERE role_code = #{roleCode}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM roles
        ORDER BY create_time DESC
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT r.id, r.role_name, r.role_code, r.description, r.status, r.create_time, r.update_time
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>

    <insert id="insert" parameterType="com.coding24h.reading_share.entity.Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO roles (role_name, role_code, description, status, create_time, update_time)
        VALUES (#{roleName}, #{roleCode}, #{description}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <update id="update" parameterType="com.coding24h.reading_share.entity.Role">
        UPDATE roles
        SET role_name = #{roleName},
            role_code = #{roleCode},
            description = #{description},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM roles WHERE id = #{id}
    </delete>

</mapper>

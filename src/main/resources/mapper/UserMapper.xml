<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.coding24h.reading_share.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="UserWithRolesResultMap" type="com.coding24h.reading_share.entity.User" extends="BaseResultMap">
        <collection property="roles" ofType="com.coding24h.reading_share.entity.Role">
            <id column="role_id" property="id" jdbcType="BIGINT"/>
            <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
            <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
            <result column="role_description" property="description" jdbcType="VARCHAR"/>
            <result column="role_status" property="status" jdbcType="INTEGER"/>
            <result column="role_create_time" property="createTime" jdbcType="TIMESTAMP"/>
            <result column="role_update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id, username, password, email, nickname, avatar, status, create_time, update_time
    </sql>

    <select id="selectById" resultMap="UserWithRolesResultMap">
        SELECT u.id, u.username, u.password, u.email, u.nickname, u.avatar, u.status, u.create_time, u.update_time,
               r.id as role_id, r.role_name, r.role_code, r.description as role_description, 
               r.status as role_status, r.create_time as role_create_time, r.update_time as role_update_time
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.id = #{id}
    </select>

    <select id="selectByUsername" resultMap="UserWithRolesResultMap">
        SELECT u.id, u.username, u.password, u.email, u.nickname, u.avatar, u.status, u.create_time, u.update_time,
               r.id as role_id, r.role_name, r.role_code, r.description as role_description, 
               r.status as role_status, r.create_time as role_create_time, r.update_time as role_update_time
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        WHERE u.username = #{username}
    </select>

    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE email = #{email}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        ORDER BY create_time DESC
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectCount" resultType="int">
        SELECT COUNT(*)
        FROM users
    </select>

    <select id="selectByUsernameLike" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE username LIKE CONCAT('%', #{username}, '%')
        ORDER BY create_time DESC
    </select>

    <insert id="insert" parameterType="com.coding24h.reading_share.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (username, password, email, nickname, avatar, status, create_time, update_time)
        VALUES (#{username}, #{password}, #{email}, #{nickname}, #{avatar}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <update id="update" parameterType="com.coding24h.reading_share.entity.User">
        UPDATE users
        SET username = #{username},
            password = #{password},
            email = #{email},
            nickname = #{nickname},
            avatar = #{avatar},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM users WHERE id = #{id}
    </delete>

</mapper>

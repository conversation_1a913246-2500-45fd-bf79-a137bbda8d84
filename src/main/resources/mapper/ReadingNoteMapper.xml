<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.ReadingNoteMapper">

    <resultMap id="BaseResultMap" type="com.coding24h.reading_share.entity.ReadingNote">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="book_id" property="bookId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="TEXT"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="rating" property="rating" jdbcType="INTEGER"/>
        <result column="is_public" property="isPublic" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="ReadingNoteWithDetailsResultMap" type="com.coding24h.reading_share.entity.ReadingNote" extends="BaseResultMap">
        <association property="user" javaType="com.coding24h.reading_share.entity.User">
            <id column="user_id" property="id" jdbcType="BIGINT"/>
            <result column="username" property="username" jdbcType="VARCHAR"/>
            <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
            <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        </association>
        <association property="book" javaType="com.coding24h.reading_share.entity.Book">
            <id column="book_id" property="id" jdbcType="BIGINT"/>
            <result column="book_title" property="title" jdbcType="VARCHAR"/>
            <result column="book_author" property="author" jdbcType="VARCHAR"/>
            <result column="book_cover_image" property="coverImage" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, book_id, title, content, tags, rating, is_public, status, create_time, update_time
    </sql>

    <select id="selectById" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.id = #{id}
    </select>

    <select id="selectAllPublic" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.is_public = 1 AND rn.status = 1
        ORDER BY rn.create_time DESC
    </select>

    <select id="selectByUserId" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.user_id = #{userId}
        ORDER BY rn.create_time DESC
    </select>

    <select id="selectByBookId" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.book_id = #{bookId} AND rn.is_public = 1 AND rn.status = 1
        ORDER BY rn.create_time DESC
    </select>

    <select id="selectPublicByPage" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.is_public = 1 AND rn.status = 1
        ORDER BY rn.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectPublicCount" resultType="int">
        SELECT COUNT(*)
        FROM reading_notes
        WHERE is_public = 1 AND status = 1
    </select>

    <select id="selectByTitleLike" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.title LIKE CONCAT('%', #{title}, '%') AND rn.is_public = 1 AND rn.status = 1
        ORDER BY rn.create_time DESC
    </select>

    <select id="selectByTags" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.tags LIKE CONCAT('%', #{tags}, '%') AND rn.is_public = 1 AND rn.status = 1
        ORDER BY rn.create_time DESC
    </select>

    <select id="selectByUserIdAndBookId" resultMap="ReadingNoteWithDetailsResultMap">
        SELECT rn.id, rn.user_id, rn.book_id, rn.title, rn.content, rn.tags, rn.rating, rn.is_public, rn.status, rn.create_time, rn.update_time,
               u.username, u.nickname, u.avatar,
               b.title as book_title, b.author as book_author, b.cover_image as book_cover_image
        FROM reading_notes rn
        LEFT JOIN users u ON rn.user_id = u.id
        LEFT JOIN books b ON rn.book_id = b.id
        WHERE rn.user_id = #{userId} AND rn.book_id = #{bookId}
        ORDER BY rn.create_time DESC
    </select>

    <insert id="insert" parameterType="com.coding24h.reading_share.entity.ReadingNote" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO reading_notes (user_id, book_id, title, content, tags, rating, is_public, status, create_time, update_time)
        VALUES (#{userId}, #{bookId}, #{title}, #{content}, #{tags}, #{rating}, #{isPublic}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <update id="update" parameterType="com.coding24h.reading_share.entity.ReadingNote">
        UPDATE reading_notes
        SET title = #{title},
            content = #{content},
            tags = #{tags},
            rating = #{rating},
            is_public = #{isPublic},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM reading_notes WHERE id = #{id}
    </delete>

</mapper>

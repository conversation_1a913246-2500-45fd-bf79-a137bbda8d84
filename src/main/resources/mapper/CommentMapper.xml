<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coding24h.reading_share.mapper.CommentMapper">

    <resultMap id="BaseResultMap" type="com.coding24h.reading_share.entity.Comment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="note_id" property="noteId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="content" property="content" jdbcType="TEXT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="CommentWithDetailsResultMap" type="com.coding24h.reading_share.entity.Comment" extends="BaseResultMap">
        <association property="user" javaType="com.coding24h.reading_share.entity.User">
            <id column="user_id" property="id" jdbcType="BIGINT"/>
            <result column="username" property="username" jdbcType="VARCHAR"/>
            <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
            <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        </association>
        <association property="readingNote" javaType="com.coding24h.reading_share.entity.ReadingNote">
            <id column="note_id" property="id" jdbcType="BIGINT"/>
            <result column="note_title" property="title" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        id, note_id, user_id, content, parent_id, status, create_time, update_time
    </sql>

    <select id="selectById" resultMap="CommentWithDetailsResultMap">
        SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time,
               u.username, u.nickname, u.avatar,
               rn.title as note_title
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes rn ON c.note_id = rn.id
        WHERE c.id = #{id}
    </select>

    <select id="selectByNoteId" resultMap="CommentWithDetailsResultMap">
        SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time,
               u.username, u.nickname, u.avatar,
               rn.title as note_title
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes rn ON c.note_id = rn.id
        WHERE c.note_id = #{noteId} AND c.status = 1
        ORDER BY c.create_time ASC
    </select>

    <select id="selectByUserId" resultMap="CommentWithDetailsResultMap">
        SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time,
               u.username, u.nickname, u.avatar,
               rn.title as note_title
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes rn ON c.note_id = rn.id
        WHERE c.user_id = #{userId}
        ORDER BY c.create_time DESC
    </select>

    <select id="selectByParentId" resultMap="CommentWithDetailsResultMap">
        SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time,
               u.username, u.nickname, u.avatar,
               rn.title as note_title
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes rn ON c.note_id = rn.id
        WHERE c.parent_id = #{parentId} AND c.status = 1
        ORDER BY c.create_time ASC
    </select>

    <select id="selectAll" resultMap="CommentWithDetailsResultMap">
        SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time,
               u.username, u.nickname, u.avatar,
               rn.title as note_title
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes rn ON c.note_id = rn.id
        ORDER BY c.create_time DESC
    </select>

    <select id="selectByPage" resultMap="CommentWithDetailsResultMap">
        SELECT c.id, c.note_id, c.user_id, c.content, c.parent_id, c.status, c.create_time, c.update_time,
               u.username, u.nickname, u.avatar,
               rn.title as note_title
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN reading_notes rn ON c.note_id = rn.id
        ORDER BY c.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectCount" resultType="int">
        SELECT COUNT(*)
        FROM comments
    </select>

    <insert id="insert" parameterType="com.coding24h.reading_share.entity.Comment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO comments (note_id, user_id, content, parent_id, status, create_time, update_time)
        VALUES (#{noteId}, #{userId}, #{content}, #{parentId}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <update id="update" parameterType="com.coding24h.reading_share.entity.Comment">
        UPDATE comments
        SET content = #{content},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM comments WHERE id = #{id}
    </delete>

    <delete id="deleteByNoteId">
        DELETE FROM comments WHERE note_id = #{noteId}
    </delete>

</mapper>

/* 阅读分享平台自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 卡片样式 */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #e1e5e9;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 警告框样式 */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: #d1f2eb;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

/* 响应式图片 */
.img-responsive {
    max-width: 100%;
    height: auto;
}

/* 文本截断 */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 评分星星 */
.rating {
    color: #ffc107;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    background-color: #e9ecef;
    color: #495057;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    text-decoration: none;
}

.tag:hover {
    background-color: #dee2e6;
    color: #495057;
    text-decoration: none;
}

/* 分页样式 */
.pagination {
    justify-content: center;
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #667eea;
}

.page-link:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip-inner {
    background-color: #333;
    color: white;
    border-radius: 4px;
}

/* 模态框样式 */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
}

/* 代码块样式 */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    overflow-x: auto;
}

code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
}

/* 搜索结果高亮 */
.highlight {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
}

/* 用户头像 */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-sm {
    width: 24px;
    height: 24px;
}

.avatar-lg {
    width: 80px;
    height: 80px;
}

/* 状态标签 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.status-published {
    background-color: #d1f2eb;
    color: #0c5460;
}

.status-draft {
    background-color: #fff3cd;
    color: #856404;
}

.status-private {
    background-color: #f8d7da;
    color: #721c24;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .lead {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .pagination,
    footer {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
    }
}

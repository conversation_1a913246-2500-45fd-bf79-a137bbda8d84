// 阅读分享平台JavaScript功能

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    initTooltips();
    
    // 初始化确认对话框
    initConfirmDialogs();
    
    // 初始化表单验证
    initFormValidation();
    
    // 初始化搜索功能
    initSearch();
    
    // 初始化评分功能
    initRating();
    
    // 自动隐藏警告消息
    autoHideAlerts();
});

// 初始化Bootstrap工具提示
function initTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化确认对话框
function initConfirmDialogs() {
    // 删除确认
    document.querySelectorAll('.confirm-delete').forEach(function(element) {
        element.addEventListener('click', function(e) {
            if (!confirm('确定要删除吗？此操作不可恢复。')) {
                e.preventDefault();
            }
        });
    });
    
    // 发布确认
    document.querySelectorAll('.confirm-publish').forEach(function(element) {
        element.addEventListener('click', function(e) {
            if (!confirm('确定要发布这篇笔记吗？发布后其他用户将可以看到。')) {
                e.preventDefault();
            }
        });
    });
}

// 初始化表单验证
function initFormValidation() {
    // Bootstrap表单验证
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // 密码确认验证
    var passwordField = document.getElementById('password');
    var confirmPasswordField = document.getElementById('confirmPassword');
    
    if (passwordField && confirmPasswordField) {
        function validatePassword() {
            if (passwordField.value !== confirmPasswordField.value) {
                confirmPasswordField.setCustomValidity('密码不匹配');
            } else {
                confirmPasswordField.setCustomValidity('');
            }
        }
        
        passwordField.addEventListener('input', validatePassword);
        confirmPasswordField.addEventListener('input', validatePassword);
    }
}

// 初始化搜索功能
function initSearch() {
    var searchForm = document.querySelector('form[action*="/search"]');
    if (searchForm) {
        var searchInput = searchForm.querySelector('input[name="keyword"]');
        if (searchInput) {
            // 搜索建议功能（可以后续扩展）
            searchInput.addEventListener('input', function() {
                // 这里可以添加搜索建议功能
            });
        }
    }
}

// 初始化评分功能
function initRating() {
    document.querySelectorAll('.rating-input').forEach(function(ratingContainer) {
        var stars = ratingContainer.querySelectorAll('.star');
        var hiddenInput = ratingContainer.querySelector('input[type="hidden"]');
        
        stars.forEach(function(star, index) {
            star.addEventListener('click', function() {
                var rating = index + 1;
                hiddenInput.value = rating;
                
                // 更新星星显示
                stars.forEach(function(s, i) {
                    if (i < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
            
            star.addEventListener('mouseover', function() {
                var rating = index + 1;
                
                // 临时高亮
                stars.forEach(function(s, i) {
                    if (i < rating) {
                        s.classList.add('hover');
                    } else {
                        s.classList.remove('hover');
                    }
                });
            });
        });
        
        ratingContainer.addEventListener('mouseleave', function() {
            // 移除临时高亮
            stars.forEach(function(s) {
                s.classList.remove('hover');
            });
        });
    });
}

// 自动隐藏警告消息
function autoHideAlerts() {
    document.querySelectorAll('.alert').forEach(function(alert) {
        if (alert.classList.contains('alert-success')) {
            setTimeout(function() {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000); // 5秒后自动隐藏成功消息
        }
    });
}

// 工具函数：格式化日期
function formatDate(date) {
    var options = { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    };
    return new Date(date).toLocaleDateString('zh-CN', options);
}

// 工具函数：截断文本
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

// 工具函数：显示加载状态
function showLoading(element) {
    var originalText = element.innerHTML;
    element.innerHTML = '<span class="loading"></span> 加载中...';
    element.disabled = true;
    
    return function() {
        element.innerHTML = originalText;
        element.disabled = false;
    };
}

// 工具函数：显示消息提示
function showMessage(message, type = 'info') {
    var alertClass = 'alert-' + type;
    var iconClass = type === 'success' ? 'fa-check-circle' : 
                   type === 'danger' ? 'fa-exclamation-circle' : 
                   type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';
    
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    var container = document.querySelector('.container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(function() {
                var alert = container.querySelector('.alert-success');
                if (alert) {
                    var bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    }
}

// AJAX请求封装
function ajaxRequest(url, options = {}) {
    var defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    var finalOptions = Object.assign(defaultOptions, options);
    
    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .catch(error => {
            console.error('Ajax request failed:', error);
            showMessage('请求失败，请稍后重试', 'danger');
            throw error;
        });
}

// 图片懒加载
function initLazyLoading() {
    var images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        var imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(function(img) {
            imageObserver.observe(img);
        });
    } else {
        // 降级处理
        images.forEach(function(img) {
            img.src = img.dataset.src;
        });
    }
}

// 返回顶部功能
function initBackToTop() {
    var backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTopBtn.className = 'btn btn-primary position-fixed';
    backToTopBtn.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000; display: none; border-radius: 50%; width: 50px; height: 50px;';
    backToTopBtn.setAttribute('title', '返回顶部');
    
    document.body.appendChild(backToTopBtn);
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// 初始化返回顶部功能
initBackToTop();

# ??????
spring.application.name=reading_share
server.port=8080

# ?????
spring.datasource.url=**************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis??
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.coding24h.reading_share.entity
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Spring Security??
spring.security.user.name=admin
spring.security.user.password=admin123

# Thymeleaf??
spring.thymeleaf.cache=false
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.mode=HTML
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# ??????
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# ????
logging.level.com.coding24h.reading_share.mapper=DEBUG
logging.level.org.springframework.security=DEBUG

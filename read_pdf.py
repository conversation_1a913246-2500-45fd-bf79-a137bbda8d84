#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def read_pdf_with_multiple_methods(pdf_path):
    """尝试多种方法读取PDF文件"""
    
    # 方法1: 尝试使用PyPDF2
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page_num, page in enumerate(reader.pages):
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                text += page.extract_text()
            return text
    except ImportError:
        print("PyPDF2 未安装")
    except Exception as e:
        print(f"PyPDF2 读取失败: {e}")
    
    # 方法2: 尝试使用pdfplumber
    try:
        import pdfplumber
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                text += page.extract_text() or ""
        return text
    except ImportError:
        print("pdfplumber 未安装")
    except Exception as e:
        print(f"pdfplumber 读取失败: {e}")
    
    # 方法3: 尝试使用pymupdf (fitz)
    try:
        import fitz  # PyMuPDF
        text = ""
        doc = fitz.open(pdf_path)
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text += f"\n=== 第 {page_num + 1} 页 ===\n"
            text += page.get_text()
        doc.close()
        return text
    except ImportError:
        print("PyMuPDF 未安装")
    except Exception as e:
        print(f"PyMuPDF 读取失败: {e}")
    
    return "无法读取PDF文件，请尝试安装相关库或转换为文本格式"

if __name__ == "__main__":
    # 读取综合实验说明PDF
    pdf_content = read_pdf_with_multiple_methods("Java应用开发II-综合实验说明.pdf")
    print("=== Java应用开发II-综合实验说明.pdf 内容 ===")
    print(pdf_content)
    
    # 保存到文本文件
    with open("综合实验说明.txt", "w", encoding="utf-8") as f:
        f.write(pdf_content)
    print("\n内容已保存到 综合实验说明.txt")

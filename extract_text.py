#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

def extract_text_from_pdf_simple(pdf_path):
    """简单的PDF文本提取方法"""
    try:
        # 尝试读取PDF文件的原始内容并查找可读文本
        with open(pdf_path, 'rb') as f:
            content = f.read()
            
        # 将字节转换为字符串，忽略错误
        text_content = content.decode('utf-8', errors='ignore')
        
        # 提取可能的文本片段
        lines = []
        for line in text_content.split('\n'):
            # 过滤掉太短或包含太多非文本字符的行
            if len(line) > 10 and line.count('\\') < len(line) * 0.3:
                clean_line = ''.join(c for c in line if ord(c) < 127 and c.isprintable())
                if len(clean_line) > 5:
                    lines.append(clean_line)
        
        return '\n'.join(lines[:100])  # 返回前100行
        
    except Exception as e:
        return f"提取失败: {str(e)}"

def extract_text_from_pdf_binary(pdf_path):
    """从PDF二进制内容中提取文本"""
    try:
        with open(pdf_path, 'rb') as f:
            content = f.read()
        
        # 查找PDF中的文本对象
        text_parts = []
        content_str = content.decode('latin-1', errors='ignore')
        
        # 查找BT...ET块（PDF文本块）
        import re
        text_blocks = re.findall(r'BT(.*?)ET', content_str, re.DOTALL)
        
        for block in text_blocks:
            # 查找Tj操作符前的文本
            text_matches = re.findall(r'\((.*?)\)\s*Tj', block)
            for match in text_matches:
                if len(match) > 2:
                    text_parts.append(match)
        
        return '\n'.join(text_parts[:50])
        
    except Exception as e:
        return f"二进制提取失败: {str(e)}"

if __name__ == "__main__":
    pdf_file = "Java应用开发II-综合实验说明.pdf"
    
    print("=== 方法1: 简单文本提取 ===")
    result1 = extract_text_from_pdf_simple(pdf_file)
    print(result1)
    
    print("\n=== 方法2: 二进制文本提取 ===")
    result2 = extract_text_from_pdf_binary(pdf_file)
    print(result2)
    
    # 保存结果
    with open("pdf_extracted_text.txt", "w", encoding="utf-8") as f:
        f.write("=== 方法1结果 ===\n")
        f.write(result1)
        f.write("\n\n=== 方法2结果 ===\n")
        f.write(result2)
    
    print("\n结果已保存到 pdf_extracted_text.txt")
